'use client'

/**
 * 授权医生和技师页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface DoctorsAndTechniciansContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 优化的动画配置
const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.05,
    },
  },
}

const fadeInUp = {
  initial: { opacity: 0, y: 15 },
  animate: { opacity: 1, y: 0 },
}

// 主要的 DoctorsAndTechniciansContent 组件
export default function DoctorsAndTechniciansContent({ pageData }: DoctorsAndTechniciansContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }
  const { blocks } = pageData
  return (
    <div className="bg-white pt-16 lg:bg-[#000] lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="relative"
            initial={{ scale: 1.02 }}
            animate={{ scale: 1 }}
            transition={{ duration: 1.2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-13 lg:top-10 left-7.5 z-10 w-full text-black lg:top-[40%] lg:left-[12%]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden font-[300] leading-[28px] text-sm lg:block lg:text-justify"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* Doctors and Technicians Grid */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="w-full bg-[#FAF6F2] pt-10 pb-5 lg:py-20">
          <div className="mx-auto px-4 lg:px-0 lg:w-[76%]">
            {/* 网格布局 - PC端2列，移动端1列 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 lg:gap-10">
              {blocks.map(
                (
                  block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
                  index: number
                ) => (
                  <motion.div
                    key={block.id || index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.05, ease: 'easeOut' }}
                    viewport={{ once: true, amount: 0.1 }}
                    className="group"
                  >
                    {/* 医生卡片 */}
                    <motion.div
                      className="relative overflow-hidden bg-white mb-5 lg:mb-0 cursor-pointer"
                      whileHover={{
                        y: -8,
                        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)"
                      }}
                      transition={{
                        duration: 0.3,
                        ease: "easeOut"
                      }}
                    >
                      {/* 左右布局容器 */}
                      <div className="flex flex-row gap-[30px]">
                        {/* 左侧图片区域 - 占40%宽度 */}
                        <div className=" w-[46%] flex justify-center justify-start">
                          <div className="relative w-full aspect-[3/4] overflow-hidden">
                            <motion.div
                              className="w-full h-full"
                              whileHover={{ scale: 1.05 }}
                              transition={{ duration: 0.3, ease: "easeOut" }}
                            >
                              <Image
                                src={block.media?.url || ''}
                                alt={block.title || '医生照片'}
                                fill
                                className="object-cover object-center"
                              />
                            </motion.div>
                          </div>
                        </div>

                        {/* 右侧内容区域 - 占60%宽度 */}
                        <div className="pt-4 lg:pt-[30px] w-[54%] flex flex-col justify-start">
                          {/* 医生姓名 */}
                          <motion.h3
                            className="mb-[6px] text-[20px] font-[500] text-black leading-[28px] group-hover:text-[#D2A76A] transition-colors duration-300"
                            variants={fadeInUp}
                            transition={{ duration: 0.3, ease: 'easeOut' }}
                          >
                            {block.title}
                          </motion.h3>

                          {/* 职位/科室 */}
                          {block.subtitle && (
                            <motion.p
                              className="mb-2 lg:mb-3 text-sm font-[500] text-[#D2A76A] leading-[28px]"
                              variants={fadeInUp}
                              transition={{ duration: 0.3, delay: 0.05, ease: 'easeOut' }}
                            >
                              {block.subtitle}
                            </motion.p>
                          )}

                          {/* 分割线 */}
                          <motion.div
                            className="mb-6 lg:mb-10 h-px bg-[#D2A76A] w-[30px] origin-left"
                            whileHover={{ scaleX: 1.5 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                          ></motion.div>

                          {/* 描述信息 */}
                          {block.description && (
                            <motion.div
                              className="text-left text-sm font-[300] text-[#444] leading-[28px]"
                              variants={fadeInUp}
                              transition={{ duration: 0.3, delay: 0.1, ease: 'easeOut' }}
                            >
                              {/* 将描述按行分割显示 */}
                              {block?.description?.split('\n').map((line, lineIndex) => (
                                <p key={lineIndex}>
                                  {line}
                                </p>
                              ))}
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                )
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
