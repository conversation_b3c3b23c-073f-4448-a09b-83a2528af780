{"miniprogramRoot": "dist/", "projectname": "lumii", "description": "", "appid": "wx855c230aba0f084e", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "3.9.2", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}