import React, { PureComponent } from 'react';
import Icon1 from '@/assets/CaseQualityInspection/Dashboard/icon1.png';
import Icon2 from '@/assets/CaseQualityInspection/Dashboard/icon2.png';
import Icon3 from '@/assets/CaseQualityInspection/Dashboard/icon3.png';
import upIcon from '@/assets/CaseQualityInspection/Dashboard/up.png';
import downIcon from '@/assets/CaseQualityInspection/Dashboard/down.png';
import AiIcon from '@/assets/CaseQualityInspection/Dashboard/icon4.png';
import styles from './index.less';

class Dashboard extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      selectedTime: props.selectedTime || 'MONTH',
      selectedRangeTime: props.selectedRangeTime || '0',
    };
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.selectedTime !== this.props.selectedTime ||
      prevProps.selectedRangeTime !== this.props.selectedRangeTime
    ) {
      this.setState({
        selectedTime: this.props.selectedTime,
        selectedRangeTime: this.props.selectedRangeTime,
      });
    }
  }

  renderDecimalValue = (valueStr, type = 'integer') => {
    if (!valueStr) {
      return (
        <>
          <span className={styles.cardintPart}>0.</span>
          <span className={styles.carddecimalPart}>00</span>
        </>
      );
    }

    // 转换为数字并保留两位小数
    const numValue = type === 'integer' ? parseFloat(valueStr) : parseFloat(valueStr) * 100;
    const formattedValue = numValue.toFixed(2);
    const [intPart, decimalPart] = formattedValue.split('.');

    return (
      <>
        <span className={styles.cardintPart}>{intPart}.</span>
        <span className={styles.carddecimalPart}>{decimalPart}</span>
      </>
    );
  };

  renderAvgScore = value => {
    if (!value) return '0%';
    return `${(Math.abs(value) * 100).toFixed(0)}%`;
  };

  // 文案
  renderText = () => {
    const { selectedTime, selectedRangeTime } = this.props;
    const range = {
      '0': '本',
      '-1': '上',
      '-2': '上上',
    };
    if (selectedTime === 'MONTH') {
      if (selectedRangeTime === '0') {
        return '本月病历数量';
      }

      const now = new Date();
      const targetMonth = now.getMonth() + selectedRangeTime * 1;
      const adjustedMonth = ((targetMonth % 12) + 12) % 12;
      return `${adjustedMonth + 1}月病历数量`;
    }

    return `${range[selectedRangeTime]}周病历数量`;
  };

  // ai智能推荐
  renderAiAnalysis = () => {
    const { aiAnalysis } = this.props;
     const { selectedTime, selectedRangeTime } = this.props;
    const range = {
      '0': '本',
      '-1': '上',
      '-2': '上上',
    };
    if (selectedTime === 'MONTH') {
      if (selectedRangeTime === '0') {
        return `本月暂无病历数据，尚无法生成质检总结，可点击右上角查看往期质检数据';
      }

      const now = new Date();
      const targetMonth = now.getMonth() + selectedRangeTime * 1;
      const adjustedMonth = ((targetMonth % 12) + 12) % 12;
      return `${adjustedMonth + 1}月暂无病历数据，尚无法生成质检总结，可点击右上角查看往期质检数据`;
    }

    return aiAnalysis || `${range[selectedRangeTime]}周暂无病历数据，尚无法生成质检总结，可点击右上角查看往期质检数据`;
  };

  render() {
    const {
      emrNum,
      avgScore,
      problemPercent,
      avgScoreGrowth,
      problemPercentGrowth,
      aiAnalysis,
    } = this.props;
    return (
      <div className={styles['dashboard-com']}>
        <div className={styles['dashboard-cards']}>
          {/* 本月病历数量 */}
          <div className={`${styles['dashboard-card']} ${styles['card-blue']}`}>
            <div className={styles['card-title']}>
              <img src={Icon1} alt="病历数量图标" />
              {this.renderText()}
            </div>
            <div className={styles['card-value']}>
              {emrNum || 0}
              <span className={styles['card-unit']}>个</span>
            </div>
          </div>
          {/* 平均质检评分 */}
          <div className={`${styles['dashboard-card']} ${styles['card-yellow']}`}>
            <div className={styles['card-title']}>
              <img src={Icon2} alt="平均质检评分图标" />
              平均质检评分
            </div>
            <div className={styles['card-foot']}>
              <div className={styles['card-value']}>{this.renderDecimalValue(avgScore)}</div>
              <div className={styles['card-trend']}>
                环比
                <img src={avgScoreGrowth > 0 ? upIcon : downIcon} alt="" />
                <span className={avgScoreGrowth > 0 ? styles['trend-up'] : styles['trend-down']}>
                  {this.renderAvgScore(avgScoreGrowth)}
                </span>
              </div>
            </div>
          </div>
          {/* 病历红线检出率 */}
          <div className={`${styles['dashboard-card']} ${styles['card-green']}`}>
            <div className={styles['card-title']}>
              <img src={Icon3} alt="病历红线检出率图标" />
              病历红线检出率
            </div>
            <div className={styles['card-foot']}>
              <div className={styles['card-value']}>
                {this.renderDecimalValue(problemPercent, 'percent')}
                <span className={styles.cardperCent}>%</span>
              </div>
              <div className={styles['card-trend']}>
                环比
                <img src={problemPercentGrowth > 0 ? upIcon : downIcon} alt="" />
                <span
                  className={problemPercentGrowth > 0 ? styles['trend-up'] : styles['trend-down']}
                >
                  {this.renderAvgScore(problemPercentGrowth)}
                </span>
              </div>
            </div>
          </div>
        </div>
        {/* AI智能推荐 */}
        <div className={styles['ai-recommend']}>
          <div className={styles['ai-label']}>
            <img src={AiIcon} alt="" />
          </div>
          <span className={styles['recommend-content']}>{this.renderAiAnalysis()}</span>
        </div>
      </div>
    );
  }
}

export default Dashboard;
