import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useState, useEffect } from 'react'
import logoPng from '../../assets/logo.png'

interface NavBarProps {
  logoSrc?: string
}

export default function NavBar(props: NavBarProps) {
  const [statusBarHeight, setStatusBarHeight] = useState(0)
  const src = props.logoSrc || (logoPng as unknown as string)

  useEffect(() => {
    const systemInfo = Taro.getSystemInfoSync()
    setStatusBarHeight(systemInfo.statusBarHeight || 0)
  }, [])

  const navBarHeight = 44 // 导航栏高度，单位px
  const totalHeight = statusBarHeight + navBarHeight

  return (
    <>
      {/* 状态栏背景 */}
      <View
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          height: `${statusBarHeight}px`,
          background: '#000',
          zIndex: 1000
        }}
      />
      {/* 导航栏 */}
      <View
        style={{
          position: 'fixed',
          top: `${statusBarHeight}px`, // 使用动态获取的状态栏高度
          left: 0,
          right: 0,
          background: '#000',
          color: '#fff',
          zIndex: 1000,
          height: `${navBarHeight}px`,
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <Image
          src={src}
          mode='aspectFit'
          style={{
            height: '22px',
            width: '94px',
            marginLeft: '12px',
            display: 'block'
          }}
        />
      </View>
      {/* 占位，避免内容被固定导航遮挡 */}
      <View
        style={{
          height: `${totalHeight}px`
        }}
      />
    </>
  )
}

