import { View, WebView } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'

export default function WebviewPage() {
  const router = useRouter()
  const baseUrl = process.env.TARO_APP_BASE_URL || 'https://wwwtest.lumii.com.cn'
  const defaultUrl = `${baseUrl}/zh-CN/image-beautify`
  const url = decodeURIComponent(router.params?.url || '') || defaultUrl

  return (
    <View>
      <WebView
        id='webview'
        src={url}
      />
    </View>
  )
}
