.page { background: #fff; }

/* Hero Video 区域 */
.hero-wrap {
  background: #fff;
  padding: 0;
  width: 100%;
  /* 宽高比 375:211，计算得出高度 */
  aspect-ratio: 375 / 211;
}

.hero-video {
  width: 100%;
  height: 100%;
  display: block;
}

/* Section 间距也统一采用 rpx（=原 px 的 2 倍） */
.section { margin-top: 16rpx; background: #fff; padding: 20rpx 24rpx 12rpx; }
.section-title { font-size: 32rpx; font-weight: 600; margin-bottom: 16rpx; display: block; }

.cat-scroll {
  white-space: nowrap;
  position: relative;
}

.categories {
  display: flex;
  gap: 32rpx;
  padding: 16rpx 8rpx 24rpx;
  position: relative;
}

.category {
  width: 136rpx;
  text-align: center;
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: block;
  margin: 0 auto 16rpx;
}

.label {
  font-size: 24rpx;
  color: #666;
  margin-top: 16rpx;
}

/* 时间线圆点 - 放在图片和文字之间 */
.timeline-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #d4af37;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* 时间线连接线 - 相对于圆点定位，从圆点中心延伸到下一个圆点 */
.timeline-line {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 158rpx; /* 圆点间距：136rpx(category宽度) + 32rpx(gap) */
  height: 2rpx;
  background: #d4af37;
  transform: translateY(-50%);
  z-index: 1;
}

.feed { display: flex; flex-direction: column; gap: 20rpx; }
.card { position: relative; border-radius: 20rpx; overflow: hidden; }
.card-img { width: 100%; height: 380rpx; display: block; }
.card-overlay { position: absolute; left: 0; right: 0; bottom: 0; padding: 24rpx; background: linear-gradient(180deg, rgba(0,0,0,0.0) 0%, rgba(0,0,0,0.55) 60%, rgba(0,0,0,0.78) 100%); color: #fff; }
.card-name { font-size: 28rpx; font-weight: 600; }
.card-tags { margin-top: 12rpx; display: flex; flex-wrap: wrap; gap: 12rpx; }
.tag { font-size: 20rpx; padding: 4rpx 12rpx; border: 1px solid rgba(255,255,255,0.6); border-radius: 8rpx; color: #f5e1c8; background: rgba(255,255,255,0.12); }
.card-desc { margin-top: 16rpx; font-size: 24rpx; line-height: 1.5; opacity: 0.92; }
.ai-badge { position: absolute; right: 20rpx; top: 20rpx; background: #000; color: #ffd184; padding: 12rpx 16rpx; border-radius: 999px; font-size: 20rpx; }


/* 右下角 AI 悬浮按钮 */
.ai-fab {
  position: fixed;
  right: 28rpx;
  bottom: calc(28rpx + env(safe-area-inset-bottom));
  /* 兼容旧版 iOS 写法 */
  bottom: calc(28rpx + constant(safe-area-inset-bottom));
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: #0b0b0b;
  box-shadow: 0 12rpx 28rpx rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

/* AI 悬浮按钮图标 */
.ai-fab-icon {
  width: 100rpx;
  height: 100rpx;
}
