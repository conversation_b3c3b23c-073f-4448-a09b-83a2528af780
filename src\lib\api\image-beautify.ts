/**
 * 图片美化 API
 */

import { toast } from 'sonner'
import type {
  ProcessingOptions,
  ProcessingProgress,
  ProcessingResponse,
  ApiResponse,
  OSSPrepareResponse,
  ProcessingTaskResponse,
  ProcessingQueryResponse,
  BusinessApiResponse
} from './types'

// 共享密钥 (必须与后端保持一致)
const SECRET_KEY = "B8gV3rL9zX6kP2wJ7sH5nQ1mF4tYcEaUoI0iRzD9vGpKxS3fO7hJbZtW2eYcV4lA"

// 预设ID配置
const PRESET_IDS = {
  'teeth-whitening': 'MTyunxiu1c68684d55',
  'beauty-enhancement': 'MTyunxiu171723cba4'
} as const

/**
 * 为美图预设API生成带签名的请求体
 * @param businessParams - 包含业务参数的对象
 * @returns 包含签名和时间戳的完整请求体
 */
async function createSignedRequest(businessParams: Record<string, string | number>): Promise<Record<string, string | number>> {
  // 1. 添加时间戳
  const timestamp = Date.now()
  const params: Record<string, string | number> = { ...businessParams, timestamp }

  // 2. 构造待签名的字符串 (String to Sign)
  const stringToSign = Object.keys(params)
    .sort() // 按key的字母顺序排序
    .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '') // 过滤掉空值
    .map(key => `${key}=${params[key]}`) // 拼接为 key=value
    .join('&') // 用 & 连接

  console.log("String to Sign:", stringToSign) // 调试时可以打印出来

  // 3. 使用 Web Crypto API 进行 HMAC-SHA256 签名
  const key = await crypto.subtle.importKey(
    "raw",
    new TextEncoder().encode(SECRET_KEY),
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"]
  )
  const signatureBuffer = await crypto.subtle.sign("HMAC", key, new TextEncoder().encode(stringToSign))
  const signature = Array.from(new Uint8Array(signatureBuffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')

  // 4. 构造最终的请求体
  return { ...businessParams, timestamp, signature }
}

/**
 * 获取文件后缀名
 */
function getFileSuffix(file: File): string {
  let suffix = '.png'
  if (file.name.includes('.')) {
    suffix = `.${file.name.replace(/.*\./, '')}`
  }
  return suffix
}

/**
 * 准备OSS上传
 */
async function prepareOSSUpload(file: File): Promise<OSSPrepareResponse | null> {
  const suffix = getFileSuffix(file)

  // 构建查询参数
  const params = new URLSearchParams({
    serviceName: 'tempAll',
    suffix,
    size: file.size.toString()
  })

  const url = `${process.env.NEXT_PUBLIC_API_URL}/JARVIS-SERVITIZATION-REST/servitization/oss/prepare/upload?${params}`

  try {
    const res = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!res.ok) {
      toast.error(`网络错误: ${res.status} ${res.statusText}`)
      return null
    }

    const data: BusinessApiResponse<OSSPrepareResponse> = await res.json()

    if (data.code !== '10000' || !data.data) {
      toast.error(data.content || '准备OSS上传失败')
      return null
    }

    return data.data
  } catch (error) {
    toast.error('网络连接失败，请检查网络后重试')
    return null
  }
}

/**
 * 上传文件到OSS
 */
async function uploadToOSS(file: File, ossConfig: OSSPrepareResponse): Promise<string | null> {
  const formData = new FormData()
  formData.append('key', ossConfig.ossKey)
  formData.append('OSSAccessKeyId', ossConfig.accessId)
  formData.append('policy', ossConfig.policy)
  formData.append('Signature', ossConfig.signature)
  formData.append('file', file)

  try {
    const response = await fetch(ossConfig.ossHost, {
      method: 'POST',
      body: formData,
      headers: {
        // 不设置 Content-Type，让浏览器自动设置
      },
    })

    if (!response.ok) {
      toast.error(`文件上传失败: ${response.status} ${response.statusText}`)
      return null
    }

    // 返回OSS图片地址
    return `${ossConfig.ossHost}/${ossConfig.ossKey}`
  } catch (error) {
    toast.error('文件上传失败，请检查网络后重试')
    return null
  }
}

/**
 * 上传图片到OSS
 */
export async function uploadImage(file: File): Promise<string | null> {
  // 1. 准备OSS上传
  const ossConfig = await prepareOSSUpload(file)
  if (!ossConfig) {
    return null
  }

  // 2. 上传到OSS
  const ossImageUrl = await uploadToOSS(file, ossConfig)
  return ossImageUrl
}

/**
 * 提交图片处理任务
 */
async function submitProcessingTask(
  imageUrl: string,
  mediaCode: string,
  repostUrl?: string
): Promise<string | null> {
  const url = `${process.env.NEXT_PUBLIC_API_URL}/JARVIS-SERVITIZATION-REST/servitization/lumii/meitu/realphotolocal_async`

  // 构造业务参数
  const businessParams: Record<string, string> = {
    mediaCode,
    mediaData: imageUrl
  }

  // 如果有回调地址，添加到参数中
  if (repostUrl) {
    businessParams.repostUrl = repostUrl
  }

  try {
    // 生成带签名的请求体
    const signedBody = await createSignedRequest(businessParams)

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(signedBody)
    })

    if (!response.ok) {
      toast.error(`提交处理任务失败: ${response.status} ${response.statusText}`)
      return null
    }

    const data: BusinessApiResponse<ProcessingTaskResponse> = await response.json()

    if (data.code !== '10000' || !data.data?.msg_id) {
      toast.error(data.content || '提交处理任务失败')
      return null
    }

    return data.data.msg_id
  } catch (error) {
    toast.error('网络连接失败，请检查网络后重试')
    return null
  }
}

/**
 * 查询处理结果
 */
async function queryProcessingResult(msgId: string): Promise<string | null> {
  const url = `${process.env.NEXT_PUBLIC_API_URL}/JARVIS-SERVITIZATION-REST/servitization/lumii/meitu/query`
  const params = new URLSearchParams({ msgId })

  try {
    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      toast.error(`查询处理结果失败: ${response.status} ${response.statusText}`)
      return null
    }

    const data: BusinessApiResponse<ProcessingQueryResponse> = await response.json()

    if (data.code !== '10000') {
      toast.error(data.content || '查询处理结果失败')
      return null
    }

    return data.data?.media_data || null
  } catch (error) {
    toast.error('网络连接失败，请检查网络后重试')
    return null
  }
}

/**
 * 轮询查询处理结果
 */
async function pollProcessingResult(
  msgId: string,
  onProgress?: (progress: ProcessingProgress) => void,
  maxAttempts: number = 30,
  interval: number = 2000
): Promise<string | null> {
  let attempts = 0

  while (attempts < maxAttempts) {
    attempts++

    // 更新进度
    const progress = Math.min(90, (attempts / maxAttempts) * 90)
    onProgress?.({
      stage: 'processing',
      progress,
      message: `Processing image... (${attempts}/${maxAttempts})`
    })

    const result = await queryProcessingResult(msgId)

    if (result) {
      // 处理完成
      onProgress?.({
        stage: 'completed',
        progress: 100,
        message: 'Processing completed'
      })
      return result
    }

    // 等待下次查询
    if (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, interval))
    }
  }

  toast.error('图片处理超时，请重试')
  return null
}

/**
 * 处理图片
 */
export async function processImage(
  imageUrl: string,
  options: ProcessingOptions,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<ProcessingResponse | null> {
  // 开始处理
  onProgress?.({
    stage: 'analyzing',
    progress: 0,
    message: 'Starting image processing...'
  })

  // 获取预设ID
  const mediaCode = PRESET_IDS[options.type]
  if (!mediaCode) {
    toast.error(`未知的处理类型: ${options.type}`)
    return null
  }

  // 提交处理任务
  onProgress?.({
    stage: 'analyzing',
    progress: 10,
    message: 'Submitting processing task...'
  })

  const msgId = await submitProcessingTask(imageUrl, mediaCode)
  if (!msgId) {
    return null
  }

  // 轮询查询结果
  const processedImageUrl = await pollProcessingResult(msgId, onProgress)
  if (!processedImageUrl) {
    return null
  }

  return {
    processedImageUrl
  }
}

/**
 * 下载处理后的图片
 */
export async function downloadProcessedImage(imageUrl: string): Promise<Blob | null> {
  try {
    const response = await fetch(imageUrl)

    if (!response.ok) {
      toast.error(`下载图片失败: ${response.status} ${response.statusText}`)
      return null
    }

    return response.blob()
  } catch (error) {
    toast.error('下载图片失败，请检查网络后重试')
    return null
  }
}

/**
 * 模拟 API 调用（开发阶段使用）
 */
export async function mockProcessImage(
  imageUrl: string,
  _options: ProcessingOptions,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<ApiResponse<ProcessingResponse>> {
  const stages: ProcessingProgress['stage'][] = ['analyzing', 'processing', 'optimizing', 'completed']

  for (let i = 0; i < stages.length; i++) {
    const progress = Math.round((i + 1) / stages.length * 100)
    onProgress?.({
      stage: stages[i],
      progress,
      message: `${stages[i]}...`
    })

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 800))
  }

  // 返回原图片 URL（模拟处理结果）
  return {
    success: true,
    data: {
      processedImageUrl: imageUrl,
    },
  }
}

/**
 * 获取验证码
 */

export async function getVerificationCode(params: {
  phone: string
}): Promise<boolean> {
  const url = `${process.env.NEXT_PUBLIC_API_URL}/JARVIS-SERVITIZATION-REST/servitization/lumii/leavemessage/verifyCode`

  try {
    const response = await fetch(`${url}?phone=${params.phone}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      // toast.error(`发送验证码失败: ${response.status} ${response.statusText}`)
      return false
    }

    const data: BusinessApiResponse<any> = await response.json()

    if (data.code !== '10000') {
      // toast.error(data.content || '发送验证码失败')
      return false
    }

    // toast.success('验证码发送成功')
    return true
  } catch (error) {
    // toast.error('网络连接失败，请检查网络后重试')
    return false
  }
}

/**
 * 创建留资
 */

export async function createRetention(params: {
  name: string
  phone: string
  city: string
  mediaData: string
  source: string
  verifyCode: string
}): Promise<BusinessApiResponse<unknown> | null> {
  const url = `${process.env.NEXT_PUBLIC_API_URL}/JARVIS-SERVITIZATION-REST/servitization/lumii/leavemessage/create`

  try {
    const response = await fetch(url, {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      toast.error(`提交留资失败: ${response.status} ${response.statusText}`)
      return null
    }

    const data: BusinessApiResponse<unknown> = await response.json()
    return data
  } catch (error) {
    toast.error('网络连接失败，请检查网络后重试')
    return null
  }
}
