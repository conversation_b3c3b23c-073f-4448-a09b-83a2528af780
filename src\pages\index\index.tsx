import { View, Text, Image, Video, ScrollView } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import NavBar from '../../components/NavBar'
import aiIcon from '../../assets/ai-icon.png'
import './index.css'

interface CategoryItem {
  title: string
  img: string
}

interface PostItem {
  name: string
  age: number
  tags: string[]
  desc: string
  img: string
  ai?: boolean
}

export default function Index () {
  useLoad(() => {
    console.log('Home page loaded.')
  })

  const heroVideoUrl = 'https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/cms/02_1080-9c6e867d-1a69-4df0-856a-e532d0cc2b12.mp4'

  const categories: CategoryItem[] = [
    { title: '探索与采集', img: 'https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=crop&w=120&h=120' },
    { title: '专家分析', img: 'https://images.unsplash.com/photo-1491553895911-0055eca6402d?auto=format&fit=crop&w=120&h=120' },
    { title: '预览与体验', img: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=120&h=120' },
    { title: '大师设计', img: 'https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?auto=format&fit=crop&w=120&h=120' },
    { title: '临时体验', img: 'https://images.unsplash.com/photo-1496302662116-35cc4f36df92?auto=format&fit=crop&w=120&h=120' },
    { title: '更多服务', img: 'https://images.unsplash.com/photo-1497290756760-23ac55edf36f?auto=format&fit=crop&w=120&h=120' }
  ]

  const posts: PostItem[] = [
    {
      name: 'Cici Yan',
      age: 35,
      tags: ['上市公司高管', '精英', '新晋妈妈'],
      desc: '对我而言，美，是一种由内而外与自己和谐一致的状态，以及我选择呈现给世界的方式保持一致。',
      img: 'https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/cms/%E4%BD%8D%E5%9B%BE%E5%A4%87%E4%BB%BD%402x-4b53de6b-f3c8-44dd-bd43-add2aa253422.png',
      ai: true,
    },
    {
      name: 'Cici Yan',
      age: 39,
      tags: ['金融法律事务合伙人'],
      desc: '对我而言，美，是一种由内而外与自己和谐一致的状态，以及我选择呈现给世界的方式保持一致。',
      img: 'https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/cms/%E4%BD%8D%E5%9B%BE%E5%A4%87%E4%BB%BD%203%402x-533e3c03-212f-4650-b3c9-1da2f101a36c.png',
    },
    {
      name: 'Cici Yan',
      age: 35,
      tags: ['人本教练'],
      desc: '对我而言，美，是一种由内而外与自己和谐一致的状态，以及我选择呈现给世界的方式保持一致。',
      img: 'https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/cms/%E4%BD%8D%E5%9B%BE%E5%A4%87%E4%BB%BD%202%402x-61b5a54f-208e-4b8b-bc85-b46d5346d010.png',
    },
  ]

  return (
    <View className='index page'>
      <NavBar />
      {/* Hero Video */}
      <View className='hero-wrap'>
        <Video
          className='hero-video'
          src={heroVideoUrl}
          autoplay
          loop
          muted
          showPlayBtn={false}
          showCenterPlayBtn={false}
          showProgress={false}
          showFullscreenBtn={false}
          controls={false}
          objectFit='cover'
        />
      </View>

      {/* Services Section */}
      <View className='section'>
        <Text className='section-title'>尊享之旅</Text>
        <ScrollView className='cat-scroll' scrollX>
          <View className='categories'>
            {categories.map((c, index) => (
              <View className='category' key={c.title}>
                <Image className='avatar' src={c.img} mode='aspectFill' />
                {/* 时间线圆点 */}
                <View className='timeline-dot'>
                  {/* 连接线 - 除了最后一个圆点，相对于圆点定位 */}
                  {index < categories.length - 1 && (
                    <View className='timeline-line' />
                  )}
                </View>
                <Text className='label'>{c.title}</Text>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Feed Section */}
      <View className='section'>
        <Text className='section-title'>晒美</Text>
        <View className='feed'>
          {posts.map((p, i) => (
            <View className='card' key={i}>
              <Image className='card-img' src={p.img} mode='aspectFill' />
              <View className='card-overlay'>
                <Text className='card-name'>{p.name} ｜ {p.age}岁</Text>
                <View className='card-tags'>
                  {p.tags.map((t) => (
                    <Text className='tag' key={t}>{t}</Text>
                  ))}
                </View>
                <Text className='card-desc'>{p.desc}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* AI 悬浮按钮（右下角） */}
      <View
        className='ai-fab'
        onClick={() => {
          const baseUrl = process.env.TARO_APP_BASE_URL || 'https://wwwtest.lumii.com.cn'
          const target = encodeURIComponent(`${baseUrl}/zh-CN/image-beautify`)
          Taro.navigateTo({ url: `/pages/webview/index?url=${target}` })
        }}
      >
        <Image className='ai-fab-icon' src={aiIcon} mode='aspectFit' />
      </View>
    </View>
  )
}
